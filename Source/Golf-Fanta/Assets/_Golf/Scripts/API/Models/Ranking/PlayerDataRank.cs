using System;
using System.Linq;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;

public enum GolfGeneralRankEnum
{
    Amateur = 0,
    Rookie = 1,
    Veteran = 2,
    Pro = 3,
    Champion = 4
}

[Serializable]
public class Rank : IComparable<Rank>
{
    public string Id = "RANK_AMATEUR";
    public GolfGeneralRankEnum MajorRank = GolfGeneralRankEnum.Amateur;
    public int MinorRank = 1;
    public int Elo = 0;
    public RankInfo Info;

    public Rank()
    {
    }
    
    public Rank(string rankId) : this()
    {
        ConvertFromId(rankId);
        Info = GlobalSO.RemoteConfigData.RankingConfigs.FirstOrDefault(rank => rank.id == rankId);
    }

    public Rank(int elo, string rankId) : this(rankId)
    {
        Elo = elo;
    }

    public Rank(PlayerDataRank playerDataRank) : this(playerDataRank.currentPoint, playerDataRank.rankId)
    {
    }

    private void ConvertFromId(string rankId)
    {
        Id = rankId;
        
        switch (rankId)
        {
            case "RANK_AMATEUR":
            case "RANK_DEFAULT":
                MajorRank = GolfGeneralRankEnum.Amateur;
                MinorRank = 1; 
                break;
            case "RANK_ROOKIEI":
                MajorRank = GolfGeneralRankEnum.Rookie;
                MinorRank = 1;
                break;
            case "RANK_ROOKIEII":
                MajorRank = GolfGeneralRankEnum.Rookie;
                MinorRank = 2;
                break;
            case "RANK_ROOKIEIII":
                MajorRank = GolfGeneralRankEnum.Rookie;
                MinorRank = 3;
                break;
            case "RANK_VETERANI":
                MajorRank = GolfGeneralRankEnum.Veteran;
                MinorRank = 1;
                break;
            case "RANK_VETERANII":
                MajorRank = GolfGeneralRankEnum.Veteran;
                MinorRank = 2;
                break;
            case "RANK_VETERANIII":
                MajorRank = GolfGeneralRankEnum.Veteran;
                MinorRank = 3;
                break;
            case "RANK_PROI":
                MajorRank = GolfGeneralRankEnum.Pro;
                MinorRank = 1;
                break;
            case "RANK_PROII":
                MajorRank = GolfGeneralRankEnum.Pro;
                MinorRank = 2;
                break;
            case "RANK_PROIII":
                MajorRank = GolfGeneralRankEnum.Pro;
                MinorRank = 3;
                break;
            case "RANK_CHAMPIONI":
                MajorRank = GolfGeneralRankEnum.Champion;
                MinorRank = 1;
                break;
            case "RANK_CHAMPIONII":
                MajorRank = GolfGeneralRankEnum.Champion;
                MinorRank = 2;
                break;
            case "RANK_CHAMPIONIII":
                MajorRank = GolfGeneralRankEnum.Champion;
                MinorRank = 3;
                break;
            case "RANK_CHAMPIONIV":
                MajorRank = GolfGeneralRankEnum.Champion;
                MinorRank = 4;
                break;
        }
    }

    public int CompareTo(Rank other)
    {
        return (MajorRank - other.MajorRank) * 10 + (MinorRank - other.MinorRank);
    }
    
    public static bool operator >(Rank a, Rank b)
    {
        return a.CompareTo(b) > 0;
    }

    public static bool operator <(Rank a, Rank b)
    {
        return a.CompareTo(b) < 0;
    }

    public static bool operator >=(Rank a, Rank b)
    {
        return a.CompareTo(b) >= 0;
    }

    public static bool operator <=(Rank a, Rank b)
    {
        return a.CompareTo(b) <= 0;
    }

    public bool Equals(Rank other)
    {
        return CompareTo(other) == 0;
    }
}